{"name": "language-gems", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "generate-listening-audio": "tsx scripts/generate-listening-audio.ts", "test-tts-models": "tsx scripts/test-tts-models.ts", "translate-listening-assessments": "tsx scripts/translate-listening-assessments.ts", "update-question-options-to-english": "tsx scripts/update-question-options-to-english.ts", "delete-french-german-audio": "tsx scripts/delete-french-german-audio.ts", "convert-instructions-english": "tsx scripts/convert-instructions-to-english.ts", "convert-question-data-english": "tsx scripts/convert-question-data-to-english.ts", "generate-dictation-audio": "tsx scripts/generate-dictation-sentence-audio.ts", "generate-audio": "tsx scripts/generateAudio.ts", "test-gemini-tts": "node scripts/test-gemini-tts.js", "verify-gemini-setup": "node scripts/verify-gemini-setup.js", "generate-audio:test": "tsx scripts/generateAudio.ts --test", "generate-audio:dry-run": "tsx scripts/generateAudio.ts --dry-run", "generate-vocabulary-audio": "node scripts/generate-vocabulary-audio.js", "manage-audio": "tsx scripts/manageAudio.ts", "test-audio-quality": "tsx scripts/testAudioQuality.ts", "test-polly-credentials": "tsx scripts/testPollyCredentials.ts", "test:watch": "vitest --watch", "populate-listening": "tsx scripts/populate-aqa-listening-assessments.ts", "populate-reading-comprehension": "tsx scripts/populate-reading-comprehension.ts", "generate-dictation-papers": "tsx scripts/generate-dictation-papers.ts"}, "dependencies": {"@aws-sdk/client-polly": "^3.848.0", "@google-cloud/text-to-speech": "^6.2.0", "@google/generative-ai": "^0.24.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@stripe/stripe-js": "^2.4.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-image": "^3.0.3", "@tiptap/extension-text-align": "^3.0.3", "@tiptap/react": "^3.0.3", "@tiptap/starter-kit": "^3.0.3", "@types/aws-sdk": "^0.0.42", "@types/howler": "^2.2.12", "@types/pdfkit": "^0.17.0", "@types/puppeteer": "^5.4.7", "@vercel/analytics": "^1.5.0", "aws-sdk": "^2.1692.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^6.1.0", "csv-parser": "^3.2.0", "dotenv": "^17.2.0", "framer-motion": "^12.23.9", "gsap": "^3.13.0", "howler": "^2.2.4", "iconv-lite": "^0.6.3", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lottie-react": "^2.4.1", "lucide-react": "^0.344.0", "next": "^14.2.30", "node-fetch": "^3.3.2", "openai": "^5.11.0", "pdfkit": "^0.17.1", "phaser": "^3.90.0", "pixi.js": "^8.11.0", "puppeteer-core": "^10.4.0", "react": "^18", "react-country-flag": "^3.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-icons": "^5.5.0", "reading-time": "^1.5.0", "recharts": "^3.1.0", "stripe": "^14.15.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "ignore-loader": "^0.1.2", "postcss": "^8", "tailwindcss": "^3.3.0", "tsx": "^4.20.3", "typescript": "^5", "vitest": "^1.2.2"}}