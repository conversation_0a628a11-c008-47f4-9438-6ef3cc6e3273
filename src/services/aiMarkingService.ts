import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // Only for client-side usage
});

export interface MarkingCriteria {
  questionType: 'photo-description' | 'short-message' | 'gap-fill' | 'translation' | 'extended-writing';
  language: 'es' | 'fr' | 'de';
  maxMarks: number;
  wordCountRequirement?: number;
  specificCriteria?: string[];
}

export interface MarkingResult {
  score: number;
  maxScore: number;
  percentage: number;
  feedback: string;
  detailedFeedback: {
    strengths: string[];
    improvements: string[];
    grammarErrors?: string[];
    vocabularyFeedback?: string;
  };
}

export interface QuestionResponse {
  questionId: string;
  questionType: string;
  response: any;
  criteria: MarkingCriteria;
}

export class AIMarkingService {
  private getLanguageName(code: string): string {
    const names = { 'es': 'Spanish', 'fr': 'French', 'de': 'German' };
    return names[code as keyof typeof names] || code;
  }

  private async markPhotoDescription(response: any, criteria: MarkingCriteria): Promise<MarkingResult> {
    const sentences = response.sentences || [];
    const languageName = this.getLanguageName(criteria.language);
    
    const prompt = `You are an experienced ${languageName} language teacher marking a GCSE photo description task. 
    
The student was asked to write 5 sentences about a photo. Each sentence is worth 2 marks (total 10 marks).

Student's sentences:
${sentences.map((s: string, i: number) => `${i + 1}. ${s || '[No response]'}`).join('\n')}

Marking criteria:
- Each sentence worth 2 marks
- 1 mark for communication (message conveyed)
- 1 mark for language accuracy (grammar, vocabulary)
- Must be in ${languageName}
- Sentences should relate to describing a photo

Please provide:
1. Score for each sentence (0-2 marks)
2. Total score out of ${criteria.maxMarks}
3. Specific feedback on grammar, vocabulary, and communication
4. Suggestions for improvement

Format your response as JSON with this structure:
{
  "totalScore": number,
  "sentenceScores": [number, number, number, number, number],
  "feedback": "Overall feedback",
  "strengths": ["strength1", "strength2"],
  "improvements": ["improvement1", "improvement2"],
  "grammarErrors": ["error1", "error2"]
}`;

    try {
      const completion = await openai.chat.completions.create({
        model: "gpt-4.1-nano",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 1000
      });

      const result = JSON.parse(completion.choices[0].message.content || '{}');
      
      return {
        score: result.totalScore || 0,
        maxScore: criteria.maxMarks,
        percentage: Math.round((result.totalScore / criteria.maxMarks) * 100),
        feedback: result.feedback || 'No feedback provided',
        detailedFeedback: {
          strengths: result.strengths || [],
          improvements: result.improvements || [],
          grammarErrors: result.grammarErrors || []
        }
      };
    } catch (error) {
      console.error('Error marking photo description:', error);
      return this.getDefaultResult(criteria);
    }
  }

  private async markTranslation(response: any, criteria: MarkingCriteria): Promise<MarkingResult> {
    const translations = Object.values(response).filter(t => t);
    const languageName = this.getLanguageName(criteria.language);
    
    const prompt = `You are an experienced ${languageName} language teacher marking GCSE translation tasks.

Student's translations:
${translations.map((t: any, i: number) => `${i + 1}. ${t || '[No response]'}`).join('\n')}

Each translation is worth 2 marks (total ${criteria.maxMarks} marks).

Marking criteria:
- 2 marks: Fully accurate translation with correct grammar and vocabulary
- 1 mark: Generally accurate with minor errors that don't impede communication
- 0 marks: Inaccurate or incomprehensible

Please provide detailed marking with:
1. Score for each translation (0-2 marks)
2. Total score out of ${criteria.maxMarks}
3. Specific feedback on accuracy and language use
4. Common errors and suggestions

Format as JSON:
{
  "totalScore": number,
  "translationScores": [array of scores],
  "feedback": "Overall feedback",
  "strengths": ["strength1", "strength2"],
  "improvements": ["improvement1", "improvement2"],
  "grammarErrors": ["error1", "error2"]
}`;

    try {
      const completion = await openai.chat.completions.create({
        model: "gpt-4.1-nano",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 1200
      });

      const result = JSON.parse(completion.choices[0].message.content || '{}');
      
      return {
        score: result.totalScore || 0,
        maxScore: criteria.maxMarks,
        percentage: Math.round((result.totalScore / criteria.maxMarks) * 100),
        feedback: result.feedback || 'No feedback provided',
        detailedFeedback: {
          strengths: result.strengths || [],
          improvements: result.improvements || [],
          grammarErrors: result.grammarErrors || []
        }
      };
    } catch (error) {
      console.error('Error marking translation:', error);
      return this.getDefaultResult(criteria);
    }
  }

  private async markExtendedWriting(response: any, criteria: MarkingCriteria): Promise<MarkingResult> {
    const text = response.article || response.message || '';
    const wordCount = text.split(/\s+/).filter((w: string) => w.length > 0).length;
    const languageName = this.getLanguageName(criteria.language);
    
    const prompt = `You are an experienced ${languageName} language teacher marking a GCSE extended writing task.

Student's writing:
"${text}"

Word count: ${wordCount} words (target: ~${criteria.wordCountRequirement} words)
Total marks available: ${criteria.maxMarks}

Marking criteria for extended writing:
- Content and communication (40% of marks): Relevance, detail, opinions
- Range and accuracy of language (40% of marks): Vocabulary, structures, grammar
- Pronunciation and intonation (20% of marks): Spelling, accents, flow

Please assess:
1. Content quality and task completion
2. Language accuracy and range
3. Appropriate word count
4. Overall communication effectiveness

Format as JSON:
{
  "totalScore": number,
  "contentScore": number,
  "languageScore": number,
  "feedback": "Detailed overall feedback",
  "strengths": ["strength1", "strength2"],
  "improvements": ["improvement1", "improvement2"],
  "grammarErrors": ["error1", "error2"],
  "vocabularyFeedback": "Vocabulary assessment"
}`;

    try {
      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 1500
      });

      const result = JSON.parse(completion.choices[0].message.content || '{}');
      
      return {
        score: result.totalScore || 0,
        maxScore: criteria.maxMarks,
        percentage: Math.round((result.totalScore / criteria.maxMarks) * 100),
        feedback: result.feedback || 'No feedback provided',
        detailedFeedback: {
          strengths: result.strengths || [],
          improvements: result.improvements || [],
          grammarErrors: result.grammarErrors || [],
          vocabularyFeedback: result.vocabularyFeedback
        }
      };
    } catch (error) {
      console.error('Error marking extended writing:', error);
      return this.getDefaultResult(criteria);
    }
  }

  private async markGapFill(response: any, criteria: MarkingCriteria): Promise<MarkingResult> {
    // Gap fill questions have predetermined correct answers
    // This would typically be marked automatically, but we can use AI for feedback
    const answers = Object.values(response);
    const correctCount = answers.length; // Simplified - would need correct answers to compare
    
    return {
      score: Math.min(correctCount, criteria.maxMarks),
      maxScore: criteria.maxMarks,
      percentage: Math.round((Math.min(correctCount, criteria.maxMarks) / criteria.maxMarks) * 100),
      feedback: `You answered ${correctCount} out of ${criteria.maxMarks} gap-fill questions.`,
      detailedFeedback: {
        strengths: ['Completed the gap-fill exercise'],
        improvements: ['Review grammar rules for better accuracy'],
        grammarErrors: []
      }
    };
  }

  private getDefaultResult(criteria: MarkingCriteria): MarkingResult {
    return {
      score: 0,
      maxScore: criteria.maxMarks,
      percentage: 0,
      feedback: 'Unable to mark this response automatically. Please review with a teacher.',
      detailedFeedback: {
        strengths: [],
        improvements: ['Please try again or seek help from a teacher'],
        grammarErrors: []
      }
    };
  }

  async markQuestion(response: QuestionResponse): Promise<MarkingResult> {
    const { questionType, criteria } = response;

    switch (questionType) {
      case 'photo-description':
        return await this.markPhotoDescription(response.response, criteria);
      case 'translation':
        return await this.markTranslation(response.response, criteria);
      case 'extended-writing':
        return await this.markExtendedWriting(response.response, criteria);
      case 'short-message':
        return await this.markExtendedWriting(response.response, criteria);
      case 'gap-fill':
        return await this.markGapFill(response.response, criteria);
      default:
        return this.getDefaultResult(criteria);
    }
  }

  async markFullAssessment(responses: QuestionResponse[]): Promise<{
    totalScore: number;
    maxScore: number;
    percentage: number;
    questionResults: MarkingResult[];
    overallFeedback: string;
  }> {
    const questionResults: MarkingResult[] = [];
    let totalScore = 0;
    let maxScore = 0;

    // Mark each question
    for (const response of responses) {
      const result = await this.markQuestion(response);
      questionResults.push(result);
      totalScore += result.score;
      maxScore += result.maxScore;
    }

    const percentage = Math.round((totalScore / maxScore) * 100);
    
    // Generate overall feedback
    let overallFeedback = `You scored ${totalScore} out of ${maxScore} marks (${percentage}%). `;
    
    if (percentage >= 80) {
      overallFeedback += "Excellent work! You demonstrate strong language skills.";
    } else if (percentage >= 60) {
      overallFeedback += "Good effort! Focus on accuracy and expanding your vocabulary.";
    } else if (percentage >= 40) {
      overallFeedback += "You're making progress. Review grammar rules and practice more writing.";
    } else {
      overallFeedback += "Keep practicing! Focus on basic grammar and vocabulary building.";
    }

    return {
      totalScore,
      maxScore,
      percentage,
      questionResults,
      overallFeedback
    };
  }
}
