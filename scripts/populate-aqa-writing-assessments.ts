#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Language configurations
const languages = [
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' }
];

const tiers = [
  { level: 'foundation', timeLimit: 60, totalMarks: 50 },
  { level: 'higher', timeLimit: 75, totalMarks: 45 } // Higher: Q1(10) + Q2(15) + Q3(25) = 50 marks
];

// Foundation Writing Questions Template (same structure for all languages)
const foundationQuestions = [
  // Q1: Photo Description (10 marks)
  {
    question_number: 1,
    question_type: 'photo-description',
    title: 'Question 1: Photo Description',
    instructions: 'Look at the photo and write 5 sentences about what you can see. Each sentence is worth 2 marks.',
    marks: 10,
    theme: 'Theme 1: People and lifestyle',
    topic: 'Identity and relationships with others',
    question_data: {
      photoDescription: 'A family having dinner together at home',
      photoUrl: '/images/assessments/family-dinner.jpg', // Placeholder
      sentences: 5,
      marksPerSentence: 2
    }
  },
  
  // Q2: Short Message (10 marks)
  {
    question_number: 2,
    question_type: 'short-message',
    title: 'Question 2: Short Message',
    instructions: 'Write a short message to your friend about your holidays. Write approximately 50 words. In your message, you must mention all the bullet points.',
    marks: 10,
    word_count_requirement: 50,
    theme: 'Theme 3: Communication and the world around us',
    topic: 'Travel and tourism, including places of interest',
    question_data: {
      topic: 'holidays',
      wordCount: 50,
      requirements: ['city', 'hotel', 'food', 'weather', 'beach']
    }
  },

  // Q3: Gap Fill (5 marks)
  {
    question_number: 3,
    question_type: 'gap-fill',
    title: 'Question 3: Grammar',
    instructions: 'Complete each sentence by choosing the correct word from the options given.',
    marks: 5,
    theme: 'Theme 1: People and lifestyle',
    topic: 'Identity and relationships with others',
    question_data: {
      questions: [
        {
          sentence: 'Mi hermano ……………… un coche nuevo.',
          options: ['tienes', 'tiene', 'tienen'],
          correct: 'tiene'
        },
        {
          sentence: 'Los chicos son ……………… .',
          options: ['alto', 'alta', 'altos'],
          correct: 'altos'
        },
        {
          sentence: 'Me gusta mucho el ……………… .',
          options: ['sol', 'frío', 'niebla'],
          correct: 'sol'
        },
        {
          sentence: 'Mis amigos ……………… en la escuela.',
          options: ['es', 'están', 'somos'],
          correct: 'están'
        },
        {
          sentence: 'Mañana voy a ……………… mi libro.',
          options: ['leer', 'lees', 'leemos'],
          correct: 'leer'
        }
      ]
    }
  },

  // Q4: Translation (10 marks)
  {
    question_number: 4,
    question_type: 'translation',
    title: 'Question 4: Translation',
    instructions: 'Translate these sentences into Spanish.',
    marks: 10,
    theme: 'Theme 1: People and lifestyle',
    topic: 'Identity and relationships with others',
    question_data: {
      sentences: [
        { english: 'My little sister is eight years old and likes dancing.', marks: 2 },
        { english: 'Yesterday we ate pizza in a very good Italian restaurant.', marks: 2 },
        { english: 'Next summer we are going to visit our cousins in Madrid.', marks: 2 },
        { english: 'My head hurts because I studied until very late last night.', marks: 2 },
        { english: 'I prefer to watch films at home rather than go to the cinema.', marks: 2 }
      ]
    }
  },

  // Q5: Extended Writing (15 marks)
  {
    question_number: 5,
    question_type: 'extended-writing',
    title: 'Question 5: Extended Writing',
    instructions: 'You are writing an article about your daily life and aspirations. Write approximately 90 words in Spanish. You must write something about each bullet point.',
    marks: 15,
    word_count_requirement: 90,
    theme: 'Theme 1: People and lifestyle',
    topic: 'Identity and relationships with others',
    question_data: {
      prompt: 'You are writing an article about your daily life and aspirations.',
      wordCount: 90,
      bulletPoints: [
        'What you did yesterday (past tense)',
        'What you think about your current hobbies (present tense/opinion)',
        'What you will do next weekend (future tense)'
      ]
    }
  }
];

// Higher Writing Questions Template (different structure from Foundation)
const higherQuestions = [
  // Q1: Translation (10 marks) - More advanced than Foundation
  {
    question_number: 1,
    question_type: 'translation',
    title: 'Question 1: Translation',
    instructions: 'Translate the following sentences into Spanish.',
    marks: 10,
    theme: 'Theme 1: People and lifestyle',
    topic: 'Identity and relationships with others',
    question_data: {
      sentences: [
        { english: 'I walked to the park this afternoon.', marks: 2 },
        { english: 'I am going to visit my grandparents for a week.', marks: 2 },
        { english: 'My brother drinks too much sugary drinks, but I prefer water.', marks: 2 },
        { english: 'I used to read a lot of books every month when I was a child.', marks: 2 },
        { english: 'We have just met our new neighbours.', marks: 2 }
      ]
    }
  },

  // Q2: Extended Writing (15 marks) - Same as Foundation Q5
  {
    question_number: 2,
    question_type: 'extended-writing',
    title: 'Question 2: Extended Writing',
    instructions: 'You are writing an article about your daily life and aspirations. Write approximately 90 words in Spanish. You must write something about each bullet point.',
    marks: 15,
    word_count_requirement: 90,
    theme: 'Theme 1: People and lifestyle',
    topic: 'Identity and relationships with others',
    question_data: {
      prompt: 'You are writing an article about your daily life and aspirations.',
      wordCount: 90,
      bulletPoints: [
        'What you did yesterday (past tense)',
        'What you think about your current hobbies (present tense/opinion)',
        'What you will do next weekend (future tense)'
      ]
    }
  },

  // Q3: Advanced Extended Writing (25 marks)
  {
    question_number: 3,
    question_type: 'extended-writing',
    title: 'Question 3: Extended Writing',
    instructions: 'You are writing a post for a Spanish website about free time and relaxation. Write approximately 150 words in Spanish. You must write something about both bullet points.',
    marks: 25,
    word_count_requirement: 150,
    theme: 'Theme 2: Popular culture',
    topic: 'Free-time activities',
    question_data: {
      prompt: 'You are writing a post for a Spanish website about free time and relaxation.',
      wordCount: 150,
      bulletPoints: [
        'The positive aspects of having free time and how you like to relax',
        'Something new you tried recently during your free time'
      ]
    }
  }
];

// Language-specific question adaptations
const getLanguageSpecificQuestions = (langCode: string, tier: string = 'foundation') => {
  const baseQuestions = JSON.parse(JSON.stringify(tier === 'foundation' ? foundationQuestions : higherQuestions)); // Deep copy
  
  if (tier === 'foundation') {
    // Foundation tier adaptations
    if (langCode === 'fr') {
      // Adapt for French
      baseQuestions[2].question_data.questions = [
        {
          sentence: 'Mon frère ……………… une nouvelle voiture.',
          options: ['as', 'a', 'ont'],
          correct: 'a'
        },
        {
          sentence: 'Les garçons sont ……………… .',
          options: ['grand', 'grande', 'grands'],
          correct: 'grands'
        },
        {
          sentence: 'J\'aime beaucoup le ……………… .',
          options: ['soleil', 'froid', 'brouillard'],
          correct: 'soleil'
        },
        {
          sentence: 'Mes amis ……………… à l\'école.',
          options: ['est', 'sont', 'sommes'],
          correct: 'sont'
        },
        {
          sentence: 'Demain je vais ……………… mon livre.',
          options: ['lire', 'lis', 'lisons'],
          correct: 'lire'
        }
      ];

      baseQuestions[3].instructions = 'Translate these sentences into French.';
      baseQuestions[4].instructions = 'You are writing an article about your daily life and aspirations. Write approximately 90 words in French. You must write something about each bullet point.';

    } else if (langCode === 'de') {
      // Adapt for German
      baseQuestions[2].question_data.questions = [
        {
          sentence: 'Mein Bruder ……………… ein neues Auto.',
          options: ['hast', 'hat', 'haben'],
          correct: 'hat'
        },
        {
          sentence: 'Die Jungen sind ……………… .',
          options: ['groß', 'große', 'großen'],
          correct: 'groß'
        },
        {
          sentence: 'Ich mag die ……………… sehr.',
          options: ['Sonne', 'Kälte', 'Nebel'],
          correct: 'Sonne'
        },
        {
          sentence: 'Meine Freunde ……………… in der Schule.',
          options: ['ist', 'sind', 'bin'],
          correct: 'sind'
        },
        {
          sentence: 'Morgen werde ich mein Buch ……………… .',
          options: ['lesen', 'liest', 'lese'],
          correct: 'lesen'
        }
      ];

      baseQuestions[3].instructions = 'Translate these sentences into German.';
      baseQuestions[4].instructions = 'You are writing an article about your daily life and aspirations. Write approximately 90 words in German. You must write something about each bullet point.';
    }
  } else {
    // Higher tier adaptations
    if (langCode === 'fr') {
      // Adapt Q1 translation for French
      baseQuestions[0].instructions = 'Translate the following sentences into French.';
      baseQuestions[0].question_data.sentences = [
        { english: 'I walked to the park this afternoon.', marks: 2 },
        { english: 'I am going to visit my grandparents for a week.', marks: 2 },
        { english: 'My brother drinks too much sugary drinks, but I prefer water.', marks: 2 },
        { english: 'I used to read a lot of books every month when I was a child.', marks: 2 },
        { english: 'We have just met our new neighbours.', marks: 2 }
      ];

      // Q2 and Q3 adaptations for French
      baseQuestions[1].instructions = 'You are writing an article about your daily life and aspirations. Write approximately 90 words in French. You must write something about each bullet point.';
      baseQuestions[2].instructions = 'You are writing a post for a French website about free time and relaxation. Write approximately 150 words in French. You must write something about both bullet points.';

    } else if (langCode === 'de') {
      // Adapt Q1 translation for German
      baseQuestions[0].instructions = 'Translate the following sentences into German.';
      baseQuestions[0].question_data.sentences = [
        { english: 'I walked to the park this afternoon.', marks: 2 },
        { english: 'I am going to visit my grandparents for a week.', marks: 2 },
        { english: 'My brother drinks too much sugary drinks, but I prefer water.', marks: 2 },
        { english: 'I used to read a lot of books every month when I was a child.', marks: 2 },
        { english: 'We have just met our new neighbours.', marks: 2 }
      ];

      // Q2 and Q3 adaptations for German
      baseQuestions[1].instructions = 'You are writing an article about your daily life and aspirations. Write approximately 90 words in German. You must write something about each bullet point.';
      baseQuestions[2].instructions = 'You are writing a post for a German website about free time and relaxation. Write approximately 150 words in German. You must write something about both bullet points.';
    }
  }
  
  return baseQuestions;
};

async function populateWritingAssessments() {
  try {
    console.log('✍️  Starting AQA Writing Assessment population...\n');

    // Clear existing data
    console.log('🧹 Clearing existing writing assessment data...');
    await supabase.from('aqa_writing_question_responses').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('aqa_writing_results').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('aqa_writing_assignments').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('aqa_writing_questions').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('aqa_writing_assessments').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    console.log('✅ Cleared existing data\n');

    // Create assessments for each language and tier
    for (const lang of languages) {
      for (const tier of tiers) {
        console.log(`📝 Creating ${lang.name} ${tier.level} writing assessment...`);

        const assessment = {
          title: `AQA Writing Assessment - ${tier.level.charAt(0).toUpperCase() + tier.level.slice(1)} Paper 1 (${lang.name})`,
          description: `${tier.level.charAt(0).toUpperCase() + tier.level.slice(1)} level AQA-style ${lang.name} writing assessment with 5 questions covering all writing skills`,
          level: tier.level,
          language: lang.code,
          identifier: 'paper-1',
          version: '1.0',
          total_questions: 5,
          time_limit_minutes: tier.timeLimit,
          total_marks: tier.totalMarks,
          is_active: true
        };

        const { data: assessmentData, error: assessmentError } = await supabase
          .from('aqa_writing_assessments')
          .insert(assessment)
          .select('id')
          .single();

        if (assessmentError) {
          console.error(`❌ Error creating ${lang.name} ${tier.level} assessment:`, assessmentError);
          continue;
        }

        console.log(`✅ Created ${lang.name} ${tier.level} assessment:`, assessmentData.id);

        // Get language-specific questions
        const questionsToUse = getLanguageSpecificQuestions(lang.code, tier.level);

        const questionsWithAssessmentId = questionsToUse.map((q: any) => ({
          ...q,
          assessment_id: assessmentData.id,
          difficulty_rating: tier.level === 'foundation' ? 3 : 4
        }));

        const { error: questionsError } = await supabase
          .from('aqa_writing_questions')
          .insert(questionsWithAssessmentId);

        if (questionsError) {
          console.error(`❌ Error inserting ${lang.name} ${tier.level} questions:`, questionsError);
          continue;
        }

        console.log(`✅ Inserted ${lang.name} ${tier.level} questions (${questionsToUse.length} questions)`);
      }
    }

    console.log('\n🎉 AQA Writing Assessment population completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Languages: ${languages.length} (${languages.map(l => l.name).join(', ')})`);
    console.log(`- Tiers: ${tiers.length} (${tiers.map(t => t.level).join(', ')})`);
    console.log(`- Total assessments created: ${languages.length * tiers.length}`);
    console.log(`- Questions per assessment: Foundation(${foundationQuestions.length}), Higher(${higherQuestions.length})`);
    console.log(`- Total questions: ${languages.length * (foundationQuestions.length + higherQuestions.length)}`);

    // Verify all assessments were created
    const { data: allAssessments } = await supabase
      .from('aqa_writing_assessments')
      .select('language, level, identifier, title')
      .order('language, level');

    if (allAssessments) {
      console.log('\n📋 Created Assessments:');
      allAssessments.forEach(assessment => {
        console.log(`  - ${assessment.title}`);
      });
    }

  } catch (error) {
    console.error('❌ Error populating writing assessments:', error);
    process.exit(1);
  }
}

// Run the population script
if (require.main === module) {
  populateWritingAssessments()
    .then(() => {
      console.log('\n✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Script failed:', error);
      process.exit(1);
    });
}

export { populateWritingAssessments };
